# !/usr/bin/python3
# -- coding: utf-8 --
# -------------------------------
# 咪咕快游
# <AUTHOR>
# @Time 2024.09.28
# @Description
# ✨ 功能：
#     咪咕快游网页端签到，以及手机端观看广告视频得免费时长
# ✨ 抓包步骤：
#     1.    打开电脑浏览器，打开咪咕快游PC 网页
#           F12 打开开发人员工具，选择应用程序，本地存储，点击 {CK_URL} 项
#           找到 cryptoSign cryptoUserId cryptoUserToken 三项对应的值
#     2.    打开咪咕快游手机 APP，打开抓包软件，进入 我的>电玩体验馆>签到
#           观看一条广告视频，在 https://betagame.migufun.com/member/newSign/v1.0.7.7/reportLookAds 请求头中
#           找到 mgHeaders 值
#     最后组装为: cryptoSign值;cryptoUserId值;cryptoUserToken值;mgHeaders值
# ✨ 变量示例：
#     export MIGUKUAIYOU_CREDENTIALS='fed9xx;04fad981845xx;0467b88ec5dxx;7JZxSVYdvPxxx'，多账号换行分割
# -------------------------------
# cron "7 0 * * *" script-path=xxx.py,tag=匹配cron用
# const $ = new Env('咪咕快游');
# -------------------------------
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad,unpad
from tools.common import BaseRun
#from typing import override
import base64
import requests
import os
import json
import time
import random

class MgkyCrypt:
    bask = "KV4lJCt3"
    bbsk = "X3gyKiZ+Q"
    bcsk = "CEjKA=="
    basi = "IyZAYV94K3"
    bbsi = "IoJSpefi"
    bcsi = "EpJA=="
    def decode(value):
        # 此函数需要实现与 v.a.decode 相同的功能
        return base64.b64decode(value).decode('utf-8')
    def encrypt_data(data):
        key = MgkyCrypt.decode(MgkyCrypt.bask + MgkyCrypt.bbsk + MgkyCrypt.bcsk)
        iv = MgkyCrypt.decode(MgkyCrypt.basi + MgkyCrypt.bbsi + MgkyCrypt.bcsi)
        cipher = AES.new(key.encode('utf-8'), AES.MODE_CBC, iv.encode('utf-8'))
        encrypted = cipher.encrypt(pad(data.encode('utf-8'), AES.block_size))
        return base64.b64encode(encrypted).decode('utf-8')

    def decrypt_data(encrypted_data):
        key = MgkyCrypt.decode(MgkyCrypt.bask + MgkyCrypt.bbsk + MgkyCrypt.bcsk)
        iv = MgkyCrypt.decode(MgkyCrypt.basi + MgkyCrypt.bbsi + MgkyCrypt.bcsi)
        cipher = AES.new(key.encode('utf-8'), AES.MODE_CBC, iv.encode('utf-8'))
        encrypted_bytes = base64.b64decode(encrypted_data)
        decrypted = unpad(cipher.decrypt(encrypted_bytes), AES.block_size)
        return decrypted.decode('utf-8')


class Run(BaseRun):
    #@override
    def init_vars(self):
        self.session = requests.Session()
        self.session.verify = False
        # self.session.proxies = {
        #     'http': 'http://**********:7890',
        #     'https': 'http://**********:7890'
        # }
        self.base_url = "http://betagame.migufun.com"
        #剩余时长
        self.remain_minutes = 0
    #@override
    def process_vars(self, info):
        split_info = info.split(";")
        header_sign = split_info[0]
        user_id = split_info[1]
        user_token = split_info[2]
        self.app_ad_mgheaders = split_info[3]
        headers = {
            "Content-Type": "application/json;charset=UTF-8",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
            "headerSign": header_sign,
            "userId": user_id,
            "userToken": user_token
        }
        self.session.headers.update(headers)
    #@override
    def process(self, info, _):
        self.logger.info("===>🆕 变量准备")
        self.process_vars(info)
        self.logger.info("===>💥 签到")
        self.sign()
        self.logger.info("===>📺️ 看广告视频")
        self.watch_video()
        self.logger.info("===>🧑 统计用户信息")
        self.get_user_info()
        self.logger.final(f"⌛️ 目前剩余时长 {self.remain_minutes} 分钟")
    def get_user_info(self):
        """获取用户信息
        """
        url = f'{self.base_url}/member/memberights/v1.0.1.5/queryMemberRightsAndTime'
        headers = dict(self.session.headers)
        headers.update({"hgv": "5rPulhA0y"})
        msgheaders = MgkyCrypt.encrypt_data(json.dumps(headers,separators=(',', ':')))
        headers.update({"mgheaders": msgheaders})
        data = '{"needPop":0,"gameId": null}'
        res = self.session.post(url=url, headers=headers, data=MgkyCrypt.encrypt_data(data))
        res_json = json.loads(MgkyCrypt.decrypt_data(res.text))
        if res.status_code == 200:
            remain_minutes = res_json['resultData']['remainTotalTime'] / 60000
            if remain_minutes:
                #self.logger.info(f"⌛️ 目前剩余时长 {remain_minutes} 分钟")
                self.remain_minutes = remain_minutes
            else:
                self.logger.info("⌛️ ❌ 剩余时长未知")
        else:
            self.logger.error("❌ 请求异常")

    def sign(self):
        """网页端签到获取奖励
        """
        url = f'{self.base_url}/member/newSign/v1.0.6.5/querySignDetails'
        data = {"querySignMonth": 0}
        res = self.session.post(url=url, json=data)
        res_json = res.json()
        if res.status_code == 200:
            sign_days = res_json['resultData']['totalSignDays']
            #query_month = res_json['resultData']['queryMonth']
            if res_json['resultData']['popResp']['popFlag'] == 0:
                self.logger.info(f"ℹ️  今日已经签到过了，已连续签到 {sign_days} 天")
            else:
                self.logger.info(f"✅ 签到成功, 已连续签到 {sign_days} 天")
        else:
            self.logger.error("❌ 请求异常")

    def watch_video(self):
        """APP 端看广告获取奖励
        """
        url = f"{self.base_url}/member/newSign/v1.0.7.7/reportLookAds"
        payload = "tE0gx3d9Iud/Svs4RmXivQ=="
        headers = {
            'User-Agent': "okhttp/3.9.1",
            'Connection': "Keep-Alive",
            'Accept-Encoding': "gzip",
            'mgHeaders': self.app_ad_mgheaders,
            'Content-Type': "application/json; charset=utf-8"
        }
        self.get_user_info()
        for i in range(1, 5):
            before_remain_minute = self.remain_minutes
            response = requests.post(url, data=payload, headers=headers)
            #检查时长是否变化来判断广告视频是否观看成功
            time.sleep(60)
            self.get_user_info()
            if before_remain_minute < self.remain_minutes and response.status_code == 200:
                self.logger.info(f"✅ 观看广告视频 {i} 成功")
            else:
                if i == 4:
                    break
                self.logger.error(f"❌ 观看广告视频 {i} 失败，超出限制或请求异常")
                self.logger.debug(f"{response.status_code}--{response.text}--{self.remain_minutes}")
                break


if __name__ == "__main__":
    app_name = "咪咕快游"
    app_env_name = "MIGUKUAIYOU_CREDENTIALS"
    local_script_name = os.path.basename(__file__)
    local_version = '2025.01.11'
    run = Run(app_name=app_name,
              app_env_name=app_env_name,
              script_version=local_version)
    run.main()
