# !/usr/bin/python3
# -- coding: utf-8 --
# -------------------------------
# AnkiWeb 登录保活
# <AUTHOR>
# @Time 2024.10.21
# @Description
# ✨ 功能：
#     定时登录网页，维持账号不被删除
# ✨ 抓包步骤：
#     无需抓包，使用登录 Ankiweb 的账号(邮箱) email 以及密码 password
#     最后组装为: email;password
# ✨ 变量示例：
#     export ANKIWEB_CREDENTIALS='<EMAIL>;123456'，多账号换行分割
# -------------------------------
# cron "19 0 * * *" script-path=xxx.py,tag=匹配cron用
# const $ = new Env('AnkiWeb 登录保活');
# -------------------------------
from tools.common import BaseRun
#from typing import override
import requests
import os
class Run(BaseRun):
    #@override
    def init_vars(self):
        self.base_url = "https://ankiweb.net/"
        self.session = requests.Session()
        self.session.headers = {
            "Content-Type": "application/octet-stream"
        }
    #@override
    def process_vars(self, info):
        username, password = info.split(";")
        text = f"{username}{password}".encode()
        text_array = bytearray(text)
        text_array.insert(0,17)
        text_array.insert(0,10)
        text_array.insert(19,6)
        text_array.insert(19,18)
        self.run_token = text_array
    #@override
    def process(self, info, _):
        self.process_vars(info)
        self.logger.info("===> 登录 AnkiWeb 账号")
        if self.login():
            self.logger.info("===> 测试登录状态")
            self.check_deck_list()
        self.logger.final("✅ 保活成功")
    def login(self):
        url = f"{self.base_url}svc/account/login"
        login_result = self.session.post(url, data=self.run_token)
        if login_result.status_code == 200:
            temp_refresh_token = login_result.text[5:]
            confirm_login_result = self.session.get(f"https://ankiuser.net/account/ankiuser-login?t={temp_refresh_token}")
            if confirm_login_result.status_code == 200:
                self.logger.info("✅ 登录成功！")
                return True
        self.logger.error("❌ 登录失败！！")
        return False
    def check_deck_list(self):
        url = f"{self.base_url}svc/decks/deck-list-info"
        data = bytearray([8,160,252,255,255,255,255,255,255,1])
        deck_list_result = self.session.post(url,data=data)
        if deck_list_result.status_code == 200:
            self.logger.info("✅ 获取卡片主页信息成功")
        else:
            self.logger.error("❌ 获取卡片主页信息失败")

if __name__ == "__main__":
    app_name = "AnkiWeb"
    app_env_name = "ANKIWEB_CREDENTIALS"
    local_script_name = os.path.basename(__file__)
    local_version = '2025.01.08'
    run = Run(app_name=app_name,
              app_env_name=app_env_name,
              script_version=local_version)
    run.main()
