# !/usr/bin/python3
# -- coding: utf-8 --
# -------------------------------
# OpenI 启智平台
# <AUTHOR>
# @Time 2024.09.07
# @Url https://openi.pcl.ac.cn/
# @Description
# ✨ 功能：
#    OpenI 启智社区I算力积分签到，积分可用于兑换算力时间
# ✨ 变量获取：
#    打开 https://openi.pcl.ac.cn, 登录账号，按 F12 打开开发者工具，找到 https://openi.pcl.ac.cn/user/login 请求的用户名和密码
#    新建一个仓库 repo，添加任意云脑任务，记录此 repo 名字
#    组装为：用户名@密码@测试仓库名
# ✨ 变量示例：
#    export OPENI_CREDENTIALS='user@password@repo'参数值，多账号换行分割
# -------------------------------
# cron "16 8 * * *" script-path=xxx.py,tag=匹配cron用
# const $ = new Env('OpenI算力积分签到');
# -------------------------------
from bs4 import BeautifulSoup
from tools.common import BaseRun
#from typing import override
import requests
import re
import os
import time
import rsa
import base64
import json


class Run(BaseRun):
    #@override
    def init_vars(self):
        self.session = requests.Session()
        self.session.verify = False
        # self.session.proxies = {
        #     'http': 'http://**********:7890',
        #     'https': 'http://**********:7890'
        # }
        self.base_url = 'https://openi.pcl.ac.cn'
        #剩余时长
        self.headers = {
            'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
            'Accept': "application/json, text/plain, */*",
            'Accept-Encoding': "gzip, deflate, br, zstd",
            'sec-ch-ua-platform': "\"Windows\"",
            'sec-ch-ua': "\"Microsoft Edge\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"",
            'sec-ch-ua-mobile': "?0",
            'sec-fetch-site': "same-origin",
            'sec-fetch-mode': "cors",
            'sec-fetch-dest': "empty",
            'accept-language': "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            'priority': "u=1, i",
        }
        # 加密的公钥
        self.publicKey="-----BEGIN PUBLIC KEY-----\nMIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC8qOB41dNhDZyhdgiIxvCYv8fS\nkfWZOCWUZ3qhl//nMDz5RjemCxCQ2C3o63kbzWW6fEKRIWydhrVIWBu8eCEe7MfT\nYFe7IeOlwDH9mLqbMDzcLjFHphXNb2rRUii+PFJovdL9ys8utCDkWTSnP2G2x1RZ\nxUfxfQqoYkMaAEio0QIDAQAB\n-----END PUBLIC KEY-----"
        #各种操作所需要的 csrf_token
        self.csrf = ''
        # 用户信息标记
        self.labels = ['当前可用算力积分', '总共获取算力积分', '总共消耗算力积分']
        self.before_info = []
        self.after_info = []
    #@override
    def process_vars(self, info):
        for i, one_info in enumerate(info.split('@')):
            if i == 0:
                self.user_name = one_info
            elif i == 1:
                self.password = one_info
            elif i == 2:
                self.repo = one_info
        if self.user_name == '' or self.password == '' or self.repo == '':
            self.logger.error(f"！！！账号信息不完整: {self.user_name} - {self.repo}")
            exit(0)
        self.session.headers.update(self.headers)
    #@override
    def process(self, info, _):
        self.logger.info("===>🆕 变量准备")
        self.process_vars(info)
        self.logger.info("===>🔛 登录账号")
        self.csrf = self.get_csrf_token()
        if self.csrf:
            self.logger.info(f"===>🧐 仓库检查 {self.user_name}/{self.repo}")
            repo_exists,issue_limit = self.repo_check()
            if repo_exists and not issue_limit:
                self.delete_repo()
                self.recreate_repo()
            elif not repo_exists:
                self.recreate_repo()
            self.logger.info("===>🧑 获取执行前用户信息")
            self.get_personal_info()
            self.logger.info("===>📅 执行任务")
            self.logger.info("==> 任务1：创建和删除 Issue")
            self.logger.info("=> 创建新的 Issue")
            self.issue_new()
            self.logger.info("=> 关闭创建的 Issue")
            self.issue_close()
            self.logger.info("==> 任务2：调试启动云脑")
            self.logger.info("=> 创建云脑任务")
            task_id = self.cloudbrain_create()
            # self.logger.info("=> 重启云脑任务")
            # self.cloudbrain_restart()
            # 延迟10秒停止云脑任务
            time.sleep(10)
            self.logger.info("=> 停止云脑任务")
            self.cloudbrain_stop(task_id)
            # 延迟10秒删除云脑任务
            time.sleep(10)
            self.logger.info("=> 删除云脑任务")
            self.cloudbrain_del(task_id)
            self.get_personal_info(end=True)
            msg = self.show_info()
            self.logger.info("===>🧑 获取执行后用户信息")
            self.logger.final(msg)

    def repo_check(self):
        """检查仓库是否存在，以及关闭的任务数量是否超标
        return: 仓库是否存在，关闭的任务数量是否合格
        """
        # 检查仓库是否存在
        url = f"{self.base_url}/{self.user_name}/{self.repo}"
        result = self.session.get(url)
        if result.status_code == 404:
            self.logger.info("❌ 仓库不存在")
            return False, False
        # 获取已关闭的任务列表
        url = f"{self.base_url}/{self.user_name}/{self.repo}/issues?q=&type=all&sort=&state=closed&labels=&milestone=0&assignee=0"
        result = self.session.get(url)
        soup = BeautifulSoup(result.text, "html.parser")
        element_a = soup.select(f'#issue-filters a[href="/{self.user_name}/{self.repo}/issues?q=&type=all&sort=&state=closed&labels=&milestone=0&assignee=0"]')
        issue_count = 0
        if len(element_a) > 0:
            element_a_text = element_a[0].get_text()
            #获取 element_a_text 标签内容中的数字部分
            match = re.search(r'\d+', element_a_text)
            if match:
                issue_count = int(match.group())
        if issue_count >= 100:
            self.logger.error(f"❌ 目前关闭的任务数量为: {issue_count} , 大于等于 100 ！！！")
            return True, False
        self.logger.info(f"✅ 目前关闭的任务数量为: {issue_count}, 合格 (100)")
        return True, True
    def delete_repo(self):
         # 创建 3 个 issue
        url = f"{self.base_url}/{self.user_name}/{self.repo}/settings"
        payload = {
            "_csrf": self.csrf,
            "action": "delete",
            "repo_name": self.repo
        }
        del_result = self.session.post(url, data=payload)
        if del_result.status_code == 200:
            self.logger.info("✅ 删除仓库成功")
            return True
        else:
            self.logger.error(f"❌ 删除仓库失败: {del_result['reason']}")
            return False
    def recreate_repo(self):
        url = f"{self.base_url}/repo/fork/75657"
        payload = {
            "_csrf": self.csrf,
            "alias": self.repo,
            "uid": "157068",
            "repo_name": self.repo,
            "description": "本项目存放了利用启智云脑完成手写数字识别相关的任务，旨在为AI开发者提供云脑调试、训练和推理任务的示例。"
        }
        new_result = self.session.post(url, data=payload)
        if new_result.status_code == 200:
            self.logger.info("✅ 创建仓库成功")
            return True
        else:
            self.logger.error(f"❌ 创建仓库失败: {new_result['reason']}")
            return False
    def issue_new(self):
        # 创建 3 个 issue
        url = f"{self.base_url}/{self.user_name}/{self.repo}/issues/new"
        payload = {
            "_csrf": self.csrf,
            "title": "测试issue",
            "content": "测试issue",
            "ref": '',
            "search": '',
            "label_ids": '',
            "milestone_id": '',
            "assignee_ids": ''
        }
        for index in range(3):
            result = self.session.post(url, data=payload)
            if result.status_code == 200:
                self.logger.info(f"✅ 创建第 {index + 1} 个 Issue 成功")
            else:
                self.logger.error(f"❌ 创建第 {index + 1} 个 Issue 失败: {result.text}")
                return False
            time.sleep(2)
        return True
    def issue_close(self):
        # 删除（关闭）最近创建的 3 个 issue
        url = f"{self.base_url}/{self.user_name}/{self.repo}/issues?q=&type=all&sort=&state=open&labels=&milestone=0&assignee=0"
        result = self.session.get(url)
        soup = BeautifulSoup(result.text, "html.parser")
        issues = soup.find_all("div", class_="issue list")
        if not (issues and len(issues) > 0):
            self.logger.error(f"❌ 任务获取为空: {result['reason']}")
            return False
        inputs = issues[0].find_all("input")
        for index, input in enumerate(inputs[:3]):
            issue_id = input["data-issue-id"]
            url = f"{self.base_url}/{self.user_name}/{self.repo}/issues/status"
            payload = {
                "_csrf": self.csrf,
                "action": 'close',
                "issue_ids": issue_id,
                "is_add": '',
            }
            result = self.session.post(url, data=payload)
            data = result.json()
            if data.get('ok'):
                self.logger.info(f"✅ 关闭第 {index + 1} 个 Issue 成功")
            else:
                self.logger.error(f"❌ 关闭第 {index + 1} 个 Issue 失败: {result.text}")
                return False
            time.sleep(2)
    def cloudbrain_create(self):
        url = f"{self.base_url}/api/v1/{self.user_name}/{self.repo}/ai_task/create"
        params = {
            '_csrf': self.csrf
        }
        payload = {
            "repoOwnerName": self.user_name,
            "repoName": self.repo,
            "job_type": "DEBUG",
            "cluster": "C2Net",
            "compute_source": "MLU",
            "display_job_name": f"qianf{int(time.time() * 1000)}",
            "description": "",
            "branch_name": "master",
            "pretrain_model_id_str": "",
            "image_url": "swr.cn-south-1.myhuaweicloud.com/openioctopus/cambricon-pytorch:v1.0.7",
            "dataset_uuid_str": "",
            "has_internet": 2,
            "spec_id": 184,
            "_csrf": self.csrf
        }
        headers = dict(self.session.headers)
        headers.update({
            'content-type': "application/json;charset=UTF-8",
        })
        response = self.session.post(url, params=params, data=json.dumps(payload), headers=headers)
        data = response.json()
        if data.get("msg") == "ok":
            self.logger.info("✅ 创建云脑任务成功")
            return data.get("data").get("id")
        else:
            self.logger.error(f"❌ 创建云脑任务失败: {response.text}")
            return False
    def cloudbrain_restart(self,task_id):
        url = f"{self.base_url}/api/v1/{self.user_name}/{self.repo}/ai_task/restart"
        if not task_id:
            task = self.get_tasks()[0]["task"]
        else:
            task = self.get_task(task_id)
        if task["status"] == "STOPPED":
            params = {
                "id": task["id"],
                "_csrf": self.csrf
            }
            result = self.session.post(url, params=params)
            data = result.json()
            if data['msg'] == 'ok':
                self.logger.info(f"✅ {task['display_job_name']} 任务重启命令下发成功")
                return True
            else:
                self.logger.error(f"❌ {task['display_job_name']} 任务重启命令下发失败: {result.text}")
                return False
        else:
            self.logger.error(f"❌ {task['display_job_name']} 任务未停止，无法执行重启命令")
            return False
    def cloudbrain_stop(self,task_id):
        url = f"{self.base_url}/api/v1/{self.user_name}/{self.repo}/ai_task/stop"
        if not task_id:
            task = self.get_tasks()[0]["task"]
        else:
            task = self.get_task(task_id)
        if task["status"] in ("WAITING","RUNNING"):
            params = {
                "id": task["id"],
                "_csrf": self.csrf
            }
            result = self.session.post(url, params=params)
            data = result.json()
            if data['msg'] == 'ok':
                self.logger.info(f"✅ {task['display_job_name']} 任务停止命令下发成功")
                return True
            else:
                self.logger.error(f"❌ {task['display_job_name']} 任务停止命令下发失败: {result.text}")
                return False
        else:
            self.logger.error("❌ 任务未启动, 无法执行停止命令")
            return False
    def cloudbrain_del(self,task_id):
        url = f"{self.base_url}/api/v1/{self.user_name}/{self.repo}/ai_task/del"
        if not task_id:
            task = self.get_tasks()[0]["task"]
        else:
            task = self.get_task(task_id)
        if task["status"] in ("STOPPED"):
            params = {'id': task_id,'_csrf': self.csrf}
            payload = {"_csrf": self.csrf}
            headers = dict(self.session.headers)
            headers.update({'content-type': "application/json;charset=UTF-8"})
            result = self.session.post(url, params=params, headers=headers, data=json.dumps(payload))
            data = result.json()
            if data['msg'] == 'ok':
                self.logger.info(f"✅ {task['display_job_name']} 任务删除命令下发成功")
                return True
            else:
                self.logger.error(f"❌ {task['display_job_name']} 任务删除命令下发失败: {result.text}")
                return False
        else:
            self.logger.error("❌ 任务未停止, 无法执行删除命令")
            return False
    def show_info(self):
        msg = ""
        for index, label in enumerate(self.labels):
            msg += f"{label}: {self.before_info[index]} -> {self.after_info[index]}\n"
        return msg.strip()
    def get_task(self,task_id):
        url = "https://openi.pcl.ac.cn/api/v1/qianfanguojin/OpenI_Cloudbrain_Example/ai_task/brief"
        params = {
            'id': task_id,
            '_csrf': self.csrf
        }
        url = f"{self.base_url}/api/v1/{self.user_name}/{self.repo}/ai_task/brief"
        params = {
            "id": task_id,
            "_csrf": self.csrf
        }
        response = self.session.get(url, params=params)
        data = response.json()
        if data.get("msg") == "ok":
            return data.get("data")
        else:
            self.logger.error(f"❌ 获取云脑任务详情失败: {response['reason']}")
            return False
    def get_tasks(self):
        url = f"{self.base_url}/api/v1/{self.user_name}/{self.repo}/ai_task/list"
        params = None
        result = self.session.get(url, params=params)
        data = result.json()
        if data.get('msg') == 'ok':
            return data['data']["tasks"]
        else:
            self.logger.error(f"❌ 获取任务列表失败: {result.text}")
            return None
    def get_personal_info(self,end=False):
        url = f"{self.base_url}/reward/point/account"
        params = {"_csrf": self.csrf}
        result = self.session.get(url, params=params).json()
        if result.get("Msg") == 'ok':
            data = result['Data']
            total_balance = data.get('Balance', 0)
            total_points = data.get('TotalEarned', 0)
            total_consumed = data.get('TotalConsumed', 0)
            if not end:
                self.before_info = [total_balance, total_points, total_consumed]
            else:
                self.after_info = [total_balance, total_points, total_consumed]
        else:
            self.logger.error(f"❌ 获取用户信息失败: {result['error']} - {result['response']}")
    def get_csrf_token(self):
        # 用指定的公钥使用 RSA 加密密码
        pub = rsa.PublicKey.load_pkcs1_openssl_pem(self.publicKey.encode())
        password_encrypt = rsa.encrypt(self.password.encode(), pub)
        password_base64 = base64.encodebytes(password_encrypt)
        # 获取第一阶段未授权的 csrf_token，用于登录网站
        home_url = self.base_url
        home_result = self.session.get(home_url)
        unauthorized_csrf_token = home_result.cookies.get("_csrf")
        if not (home_result.status_code == 200 and unauthorized_csrf_token):
            message = home_result.get('reason', '')
            self.logger.error(f'❌ 获取未授权的 csrf_token 失败: {message}')
            return False
        self.logger.info('✅ 获取未授权的 csrf_token 成功')
        login_url = f"{self.base_url}/user/login"
        payload = {
            "user_name": self.user_name,
            "password": password_base64,
            "_csrf": unauthorized_csrf_token
        }
        login_result = self.session.post(login_url, data=payload)
        csrf_token = login_result.cookies.get("_csrf")
        # 第二阶段获取到的 csrf_token 为空或者和未授权的 csrf_token 相同，说明登录失败
        if not (login_result.status_code == 200 and csrf_token and unauthorized_csrf_token != csrf_token):
            message = home_result.get('reason', '')
            self.logger.error(f"❌ 获取授权的 csrf_token 失败：{message}")
            return False
        self.logger.info('✅ 获取授权的 csrf_token 成功')
        return csrf_token

if __name__ == '__main__':
    app_name = "Openi 启智社区"
    app_env_name = "OPENI_CREDENTIALS"
    local_script_name = os.path.basename(__file__)
    local_version = '2025.01.11'
    run = Run(app_name=app_name,
              app_env_name=app_env_name,
              script_version=local_version)
    run.main()

