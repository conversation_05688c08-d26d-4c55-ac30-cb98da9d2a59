import requests

url = "https://m.jr.airstarfinance.net/mp/api/dailyWelfare/completeTask"

payload = {
  'app': "com.mipay.wallet",
  'oaid': "444cfc5462a58934",
  'regId': "xedsq9skS5lCkxvXScyAqI5T6oWRe/zQprEr+DCi2E10CAaEh6hBQnh0eD+kX8rj",
  'versionCode': "20577603",
  'versionName': "6.91.1.5302.2333",
  'isNfcPhone': "true",
  'channel': "uma_mipay_home_icon",
  'deviceType': "2",
  'system': "1",
  'visitEnvironment': "2",
  'userExtra': "{\"platformType\":1,\"com.miui.player\":\"********\",\"com.mipay.wallet\":\"6.91.1.5302.2333\"}",
  'appType': "6",
  'miui': "true",
  'yimiId': "33",
  'browsClickUrlId': "1469142287",
  'taskCode': "GENERAL_TASK_QB4_D2C",
  'jrairstar_ph': "oD1gBeutszExghoXwW9Byw=="
}

headers = {
  'User-Agent': "Mozilla/5.0 (Linux; U; Android 15; zh-CN; 23013RK75C Build/AQ3A.240912.001; AppBundle/com.mipay.wallet; AppVersionName/6.91.1.5302.2333; AppVersionCode/20577603; MiuiVersion/stable-OS2.0.9.0.VMNCNXM; DeviceId/mondrian; NetworkType/WIFI; mix_version; WebViewVersion/136.0.7103.61) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Mobile Safari/537.36 XiaoMi/MiuiBrowser/4.3",
  'Accept': "application/json, text/plain, */*",
  'Accept-Encoding': "gzip, deflate, br, zstd",
  'X-Request-ID': "e644acb0-5dbc-48d8-b7b4-0290143dbfa9",
  'sec-ch-ua-platform': "\"Android\"",
  'Cache-Control': "no-cache",
  'sec-ch-ua': "\"Chromium\";v=\"136\", \"Android WebView\";v=\"136\", \"Not.A/Brand\";v=\"99\"",
  'sec-ch-ua-mobile': "?1",
  'Origin': "https://m.jr.airstarfinance.net",
  'X-Requested-With': "com.mipay.wallet",
  'Sec-Fetch-Site': "same-origin",
  'Sec-Fetch-Mode': "cors",
  'Sec-Fetch-Dest': "empty",
  'Accept-Language': "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
  'Cookie': "cUserId=Ia2-L5qu3xu-2Z1_lersy1r22iE; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%22Ia2-L5qu3xu-2Z1_lersy1r22iE%22%2C%22first_id%22%3A%22194836f06dc298-0815d7afa739a98-54487414-376980-194836f06dd28d%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfbG9naW5faWQiOiJJYTItTDVxdTN4dS0yWjFfbGVyc3kxcjIyaUUiLCIkaWRlbnRpdHlfY29va2llX2lkIjoiMTk0ODM2ZjA2ZGMyOTgtMDgxNWQ3YWZhNzM5YTk4LTU0NDg3NDE0LTM3Njk4MC0xOTQ4MzZmMDZkZDI4ZCJ9%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%24identity_login_id%22%2C%22value%22%3A%22Ia2-L5qu3xu-2Z1_lersy1r22iE%22%7D%2C%22%24device_id%22%3A%22194836f06dc298-0815d7afa739a98-54487414-376980-194836f06dd28d%22%7D; jrairstar_serviceToken=vKCn0n6r/Nnnc/5JJfAyM8+cTvjKzmEnwqASVVMXXO0BSk8PrE6D4ibCp7b4MOybWoPMS5d3U6SY7yfwZHS1BMcLipZg9y+I+q5mIqlseokslUf6xXjG+dDhaxOkXlfNoxkHWKvOci85I2HXHjbPQY29AVpAc6LJYkzCeFqbnT/DDN0FK48OOMaVrStebFX7nn4k44MeaNiFIv+NX2DTNw==; jrairstar_slh=uxUwRTUGtmwwUxsBy7lk9SFAXss=; jrairstar_ph=oD1gBeutszExghoXwW9Byw=="
}

response = requests.post(url, data=payload, headers=headers)

print(response.text)

import requests

url = "https://m.jr.airstarfinance.net/mp/api/dailyWelfare/taskDetail?taskCode=SIGN_WALLET_FULI_D2C"

headers = {
  'User-Agent': "Mozilla/5.0 (Linux; U; Android 15; zh-CN; 23013RK75C Build/AQ3A.240912.001; AppBundle/com.mipay.wallet; AppVersionName/6.91.1.5302.2333; AppVersionCode/20577603; MiuiVersion/stable-OS2.0.9.0.VMNCNXM; DeviceId/mondrian; NetworkType/WIFI; mix_version; WebViewVersion/136.0.7103.61) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Mobile Safari/537.36 XiaoMi/MiuiBrowser/4.3",
  'Accept': "application/json, text/plain, */*",
  'Accept-Encoding': "gzip, deflate, br, zstd",
  'X-Request-ID': "21422238-b61e-45c7-9ba6-79c8f3af75a5",
  'sec-ch-ua-platform': "\"Android\"",
  'Cache-Control': "no-cache",
  'sec-ch-ua': "\"Chromium\";v=\"136\", \"Android WebView\";v=\"136\", \"Not.A/Brand\";v=\"99\"",
  'sec-ch-ua-mobile': "?1",
  'X-Requested-With': "com.mipay.wallet",
  'Sec-Fetch-Site': "same-origin",
  'Sec-Fetch-Mode': "cors",
  'Sec-Fetch-Dest': "empty",
  'Accept-Language': "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
  'Cookie': "cUserId=Ia2-L5qu3xu-2Z1_lersy1r22iE; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%22Ia2-L5qu3xu-2Z1_lersy1r22iE%22%2C%22first_id%22%3A%22194836f06dc298-0815d7afa739a98-54487414-376980-194836f06dd28d%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfbG9naW5faWQiOiJJYTItTDVxdTN4dS0yWjFfbGVyc3kxcjIyaUUiLCIkaWRlbnRpdHlfY29va2llX2lkIjoiMTk0ODM2ZjA2ZGMyOTgtMDgxNWQ3YWZhNzM5YTk4LTU0NDg3NDE0LTM3Njk4MC0xOTQ4MzZmMDZkZDI4ZCJ9%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%24identity_login_id%22%2C%22value%22%3A%22Ia2-L5qu3xu-2Z1_lersy1r22iE%22%7D%2C%22%24device_id%22%3A%22194836f06dc298-0815d7afa739a98-54487414-376980-194836f06dd28d%22%7D; jrairstar_serviceToken=vKCn0n6r/Nnnc/5JJfAyM8+cTvjKzmEnwqASVVMXXO0BSk8PrE6D4ibCp7b4MOybWoPMS5d3U6SY7yfwZHS1BMcLipZg9y+I+q5mIqlseokslUf6xXjG+dDhaxOkXlfNoxkHWKvOci85I2HXHjbPQY29AVpAc6LJYkzCeFqbnT/DDN0FK48OOMaVrStebFX7nn4k44MeaNiFIv+NX2DTNw==; jrairstar_slh=uxUwRTUGtmwwUxsBy7lk9SFAXss=; jrairstar_ph=oD1gBeutszExghoXwW9Byw=="
}

response = requests.get(url, headers=headers)

print(response.text)
