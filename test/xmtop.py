#1. 米金
import requests
import json

url = "https://m.mi.com/mtop/navi/venue/batch?page_id=13880&pdl=mishop&sign=1a04e866dd9d421e5e16e21bb984b947"

payload = {
  "query_list": [
    {
      "resolver": "infinite-task",
      "sign": "ff8960139490adb9071ed47a34f179ff",
      "parameter": "{\"actId\":\"6706c0695404a23dfb5b2cab\",\"taskTypeList\":[101,200,110,201,202]}",
      "variable": {}
    }
  ]
}

headers = {
  'User-Agent': "Mozilla/5.0 (Linux; U; Android 15; zh-CN; 23013RK75C Build/AQ3A.240912.001; AppBundle/com.mipay.wallet; AppVersionName/6.91.1.5302.2333; AppVersionCode/20577603; MiuiVersion/stable-OS2.0.9.0.VMNCNXM; DeviceId/mondrian; NetworkType/WIFI; mix_version; WebViewVersion/136.0.7103.61) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Mobile Safari/537.36 XiaoMi/MiuiBrowser/4.3",
  'Accept-Encoding': "gzip, deflate, br, zstd",
  'Content-Type': "application/json",
  'sec-ch-ua-platform': "\"Android\"",
  'x-user-agent': "channel/mishop platform/mishop.m",
  'equipmenttype': "4",
  'sec-ch-ua': "\"Chromium\";v=\"136\", \"Android WebView\";v=\"136\", \"Not.A/Brand\";v=\"99\"",
  'sec-ch-ua-mobile': "?1",
  'origin': "https://m.mi.com",
  'x-requested-with': "com.mipay.wallet",
  'sec-fetch-site': "same-origin",
  'sec-fetch-mode': "cors",
  'sec-fetch-dest': "empty",
  'referer': "https://m.mi.com/mfbs/dghd/m_currecy?_rt=rn&pageid=13880&pdl=mishop&sign=1a04e866dd9d421e5e16e21bb984b947&mute=*******&muteType=1&g_utm=MIUI.App.Icon.MiPay.0507MJSC-CT",
  'accept-language': "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
  'priority': "u=1, i",
  'Cookie': "sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%22Ia2-L5qu3xu-2Z1_lersy1r22iE%22%2C%22first_id%22%3A%221948dd23b014c1-0ed450a0b18cad-54487414-376980-1948dd23b02922%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfbG9naW5faWQiOiJJYTItTDVxdTN4dS0yWjFfbGVyc3kxcjIyaUUiLCIkaWRlbnRpdHlfY29va2llX2lkIjoiMTk0OGRkMjNiMDE0YzEtMGVkNDUwYTBiMThjYWQtNTQ0ODc0MTQtMzc2OTgwLTE5NDhkZDIzYjAyOTIyIn0%3D%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%24identity_login_id%22%2C%22value%22%3A%22Ia2-L5qu3xu-2Z1_lersy1r22iE%22%7D%2C%22%24device_id%22%3A%221948dd23b014c1-0ed450a0b18cad-54487414-376980-1948dd23b02922%22%7D; _utm_data={\"mtm\":\"MIUI.App.Icon.MiPay.0507MJSC-CT\",\"device_id\":\"\"}; serviceToken=Ru9ixjG8lImSu0tdaRhNAiONfk7gimahikam0xxefK7QEsb12tTxMsIJ%2FWB8LQhD%2FoSCsmG8EnAjh2VWKkrYCKH81MCboeBeogRgUhkS%2BN9rUYmaZGIfmpM5dSvzzFr0M9PL3eDEwTNbx2PhpHKc7ZyfPLDcrLOVS6RJluY4%2B%2FI2QE5Y%2BKoSL6L1RLCYoJI8J8gOP%2BSYO95RTXP1IfboN4CBdxaGLbpyKt%2BFSUR9RZY%3D; mishoptag=gq9DVgUNwgBIryXalBfLt/fadUyr5ZUsoSxuXkfNo48=; xm_order_btauth=4918466c1bb48588967ddf893819dea9; xm_link_history=WXPoCFCTwmlcULInmX+myVc+JDb8txlYRpK4g3XlPivFlfMc9eQ3jbIRSSJfMQ+D; smidV2=20250521135404fd7d2af19f7203f3f53c3e95573c0fb20074c7fdccdc2a5b0; mjrmicom_slh=CscMwAaFRpRdP6KJzHVYipCAC+8=; youpindistinct_id=196f167e7aa-05138e5db515128-747f; mjclient=M; mishopDeviceId=B0wsL3/JeDDuiZgn3nXk4Sf+yBEYNrt+Zvrybb53bIq8YfRe+JkQtZ2tTwfxzG3t3JN3zzeXg0qvime+0yJk1VA; youpin_sessionid=1972a46bff1-0edf8522ad9d17-73fc; .thumbcache_2959214a7e70e5f2bd017e23a2a3e462=S0DSa5u0mfrBkZWiE+7ZQC2hDd+npHljux5My66wc12/Oy27bH3z1JYyt1E2kvz21x+ugavP4WiQDi5kFbHe9g%3D%3D"
}

response = requests.post(url, data=json.dumps(payload), headers=headers)
res_json = response.json()
tasks = res_json['data']['result_list'][0]['components']


def do_task(task):
  import requests
  import json

  print(task['taskName'])
  if task['status'] != 2:
    return
  mtop_cookie = "serviceToken=Ru9ixjG8lImSu0tdaRhNAiONfk7gimahikam0xxefK7QEsb12tTxMsIJ%2FWB8LQhD%2FoSCsmG8EnAjh2VWKkrYCKH81MCboeBeogRgUhkS%2BN9rUYmaZGIfmpM5dSvzzFr0M9PL3eDEwTNbx2PhpHKc7ZyfPLDcrLOVS6RJluY4%2B%2FI2QE5Y%2BKoSL6L1RLCYoJI8J8gOP%2BSYO95RTXP1IfboN4CBdxaGLbpyKt%2BFSUR9RZY%3D;"

  mtop_header = {
    'User-Agent': "Mozilla/5.0 (Linux; U; Android 15; zh-CN; 23013RK75C Build/AQ3A.240912.001; AppBundle/com.mipay.wallet; AppVersionName/6.91.1.5302.2333; AppVersionCode/20577603; MiuiVersion/stable-OS2.0.9.0.VMNCNXM; DeviceId/mondrian; NetworkType/WIFI; mix_version; WebViewVersion/136.0.7103.61) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Mobile Safari/537.36 XiaoMi/MiuiBrowser/4.3",
    'Accept-Encoding': "gzip, deflate, br, zstd",
    'Content-Type': "application/json",
    'sec-ch-ua-platform': "\"Android\"",
    'x-user-agent': "channel/mishop platform/mishop.m",
    'equipmenttype': "4",
    'sec-ch-ua': "\"Chromium\";v=\"136\", \"Android WebView\";v=\"136\", \"Not.A/Brand\";v=\"99\"",
    'sec-ch-ua-mobile': "?1",
    'origin': "https://m.mi.com",
    'x-requested-with': "com.mipay.wallet",
    'sec-fetch-site': "same-origin",
    'sec-fetch-mode': "cors",
    'sec-fetch-dest': "empty",
    'accept-language': "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
    'priority': "u=1, i",
    'Cookie': mtop_cookie
  }

  # 完成任务
  do_url = "https://m.mi.com/mtop/mf/act/infinite/do"
  payload = [
    {},
    {
      "taskId": task['taskId'],
      "actId": task['actId']
    }
  ]
  do_res = requests.post(do_url, data=json.dumps(payload), headers=mtop_header)
  res_json = do_res.json()
  taskToken = res_json['data']['taskToken']

  # 领取奖励
  done_url = "https://m.mi.com/v2/mtop/act/lego/task/done/v2"
  if task['taskType'] == 110:
    done_url = "https://m.mi.com/mtop/mf/act/infinite/done"
    pass
  elif task['taskType'] != 200:
    return

  payload = [
    {},
    {
      "taskToken": taskToken,
      "actId": task['actId'],
      "taskType": task['taskType']
    }
  ]
  done_res = requests.post(done_url, data=json.dumps(payload), headers=mtop_header)
  done_res_json = done_res.json()
  print(done_res_json)
  pass


for task in tasks:
  do_task(task)
